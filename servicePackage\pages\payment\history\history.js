// pages/payment/history/history.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    isAuthenticated: false,
    userName: '',
    selectedCommunity: '',
    darkMode: false,

    // 筛选相关
    timeFilter: 'all', // 时间筛选：all, month3, month6, year1
    typeFilter: 'all', // 类型筛选：all, property, parking, utility
    statusFilter: 'all', // 状态筛选：all, paid, unpaid, overdue

    // 缴费记录数据
    paymentRecords: [],
    filteredRecords: [],

    // 加载状态
    isLoading: true,
    loadingMore: false,
    refreshing: false,
    hasMore: true,

    // 空状态
    showEmpty: false,
    emptyText: '暂无缴费记录',
    emptyActionText: '刷新',

    // 分页
    page: 1,
    pageSize: 10,

    // 统计数据
    totalPaid: 0,
    totalUnpaid: 0,
    totalOverdue: 0
  },

  onLoad: function (options) {
    this.checkAuthStatus()
    this.checkCommunitySelection()
    this.loadPaymentRecords()
  },

  onShow: function () {
    this.checkAuthStatus()
    this.checkCommunitySelection()
  },

  onPullDownRefresh: function () {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    })
    this.loadPaymentRecords(true)
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMoreRecords()
    }
  },



  // 检查认证状态
  checkAuthStatus: function () {
    const isAuthenticated = util.checkAuthentication()
    const userName = wx.getStorageSync('userName') || ''

    this.setData({
      isAuthenticated,
      userName
    })
  },

  // 检查社区选择
  checkCommunitySelection: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity') || ''

    this.setData({
      selectedCommunity
    })
  },

  // 加载缴费记录
  loadPaymentRecords: function (isPullDown = false) {
    this.setData({
      isLoading: true
    })

    // 模拟加载数据
    setTimeout(() => {
      // 模拟数据
      const records = this.getMockPaymentRecords()

      // 计算统计数据
      const stats = this.calculateStats(records)

      this.setData({
        paymentRecords: records,
        filteredRecords: this.filterRecords(records),
        isLoading: false,
        refreshing: false,
        showEmpty: records.length === 0,
        totalPaid: stats.totalPaid,
        totalUnpaid: stats.totalUnpaid,
        totalOverdue: stats.totalOverdue
      })

      if (isPullDown) {
        wx.stopPullDownRefresh()
      }
    }, 1000)

    // TODO: 实际API调用
    // return util.request({
    //   url: '/api/payment/records',
    //   method: 'GET',
    //   data: {
    //     page: this.data.page,
    //     pageSize: this.data.pageSize
    //   }
    // }).then(res => {
    //   // 处理返回数据
    // })
  },

  // 加载更多记录
  loadMoreRecords: function () {
    if (!this.data.hasMore) return

    this.setData({
      loadingMore: true,
      page: this.data.page + 1
    })

    // 模拟加载更多数据
    setTimeout(() => {
      // 如果页码大于3，模拟没有更多数据
      if (this.data.page > 3) {
        this.setData({
          hasMore: false,
          loadingMore: false
        })
        return
      }

      // 模拟新数据
      const newRecords = this.getMockPaymentRecords(this.data.page)
      const allRecords = [...this.data.paymentRecords, ...newRecords]

      this.setData({
        paymentRecords: allRecords,
        filteredRecords: this.filterRecords(allRecords),
        loadingMore: false
      })
    }, 1000)
  },

  // 筛选记录
  filterRecords: function (records) {
    if (!records) return []

    return records.filter(record => {
      // 时间筛选
      if (this.data.timeFilter !== 'all') {
        const recordDate = new Date(record.paymentDate || record.dueDate)
        const now = new Date()

        if (this.data.timeFilter === 'month3' &&
            (now - recordDate) > 90 * 24 * 60 * 60 * 1000) {
          return false
        }

        if (this.data.timeFilter === 'month6' &&
            (now - recordDate) > 180 * 24 * 60 * 60 * 1000) {
          return false
        }

        if (this.data.timeFilter === 'year1' &&
            (now - recordDate) > 365 * 24 * 60 * 60 * 1000) {
          return false
        }
      }

      // 类型筛选
      if (this.data.typeFilter !== 'all' && record.type !== this.data.typeFilter) {
        return false
      }

      // 状态筛选
      if (this.data.statusFilter !== 'all' && record.status !== this.data.statusFilter) {
        return false
      }

      return true
    })
  },

  // 计算统计数据
  calculateStats: function (records) {
    let totalPaid = 0
    let totalUnpaid = 0
    let totalOverdue = 0

    records.forEach(record => {
      if (record.status === 'paid') {
        totalPaid += parseFloat(record.amount)
      } else if (record.status === 'unpaid') {
        totalUnpaid += parseFloat(record.amount)
      } else if (record.status === 'overdue') {
        totalOverdue += parseFloat(record.amount)
      }
    })

    return {
      totalPaid,
      totalUnpaid,
      totalOverdue
    }
  },

  // 获取模拟数据
  getMockPaymentRecords: function (page = 1) {
    // 基础数据
    const baseRecords = [
      {
        id: '1001',
        title: '物业管理费',
        type: 'property',
        amount: '720.00',
        period: '2023年10月-12月',
        status: 'paid',
        paymentDate: '2023-12-26 18:30',
        paymentMethod: '微信支付',
        invoiceAvailable: true
      },
      {
        id: '1002',
        title: '地下停车费',
        type: 'parking',
        amount: '450.00',
        period: '2023年第四季度',
        status: 'paid',
        paymentDate: '2023-12-26 18:30',
        paymentMethod: '微信支付',
        invoiceAvailable: true
      },
      {
        id: '1003',
        title: '水电费',
        type: 'utility',
        amount: '88.50',
        period: '2023年11月',
        status: 'paid',
        paymentDate: '2023-12-26 18:30',
        paymentMethod: '微信支付',
        invoiceAvailable: true
      },
      {
        id: '1004',
        title: '物业管理费',
        type: 'property',
        amount: '720.00',
        period: '2023年7月-9月',
        status: 'paid',
        paymentDate: '2023-09-15 10:20',
        paymentMethod: '支付宝',
        invoiceAvailable: true
      },
      {
        id: '1005',
        title: '水电费',
        type: 'utility',
        amount: '102.30',
        period: '2023年10月',
        status: 'paid',
        paymentDate: '2023-11-05 14:30',
        paymentMethod: '微信支付',
        invoiceAvailable: true
      }
    ]

    // 根据页码返回不同的数据
    if (page === 1) {
      return baseRecords
    } else if (page === 2) {
      return [
        {
          id: '1006',
          title: '物业管理费',
          type: 'property',
          amount: '720.00',
          period: '2023年4月-6月',
          status: 'paid',
          paymentDate: '2023-06-20 09:15',
          paymentMethod: '银行卡',
          invoiceAvailable: true
        },
        {
          id: '1007',
          title: '地下停车费',
          type: 'parking',
          amount: '450.00',
          period: '2023年第三季度',
          status: 'paid',
          paymentDate: '2023-09-10 16:45',
          paymentMethod: '微信支付',
          invoiceAvailable: true
        },
        {
          id: '1008',
          title: '水电费',
          type: 'utility',
          amount: '95.60',
          period: '2023年9月',
          status: 'paid',
          paymentDate: '2023-10-08 11:20',
          paymentMethod: '微信支付',
          invoiceAvailable: true
        }
      ]
    } else {
      return [
        {
          id: '1009',
          title: '物业管理费',
          type: 'property',
          amount: '720.00',
          period: '2023年1月-3月',
          status: 'paid',
          paymentDate: '2023-03-25 13:40',
          paymentMethod: '微信支付',
          invoiceAvailable: true
        },
        {
          id: '1010',
          title: '地下停车费',
          type: 'parking',
          amount: '450.00',
          period: '2023年第二季度',
          status: 'paid',
          paymentDate: '2023-06-15 10:30',
          paymentMethod: '支付宝',
          invoiceAvailable: true
        }
      ]
    }
  },

  // 切换时间筛选
  changeTimeFilter: function (e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      timeFilter: filter
    })
    this.updateFilteredRecords()
  },

  // 切换类型筛选
  changeTypeFilter: function (e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      typeFilter: filter
    })
    this.updateFilteredRecords()
  },

  // 切换状态筛选
  changeStatusFilter: function (e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      statusFilter: filter
    })
    this.updateFilteredRecords()
  },

  // 更新筛选后的记录
  updateFilteredRecords: function () {
    const filtered = this.filterRecords(this.data.paymentRecords)
    this.setData({
      filteredRecords: filtered,
      showEmpty: filtered.length === 0
    })

    // 更新空状态文本
    this.updateEmptyStateText()
  },

  // 更新空状态文本
  updateEmptyStateText: function () {
    let emptyText = '暂无缴费记录'
    let actionText = '刷新'

    if (this.data.statusFilter === 'unpaid') {
      emptyText = '暂无待支付的费用'
      actionText = '查看全部记录'
    } else if (this.data.statusFilter === 'overdue') {
      emptyText = '太好了！没有逾期费用'
      actionText = '查看全部记录'
    } else if (this.data.timeFilter !== 'all' || this.data.typeFilter !== 'all') {
      emptyText = '没有符合条件的缴费记录'
      actionText = '重置筛选'
    }

    this.setData({
      emptyText,
      emptyActionText: actionText
    })
  },

  // 重置筛选
  resetFilters: function () {
    this.setData({
      timeFilter: 'all',
      typeFilter: 'all',
      statusFilter: 'all'
    })
    this.updateFilteredRecords()
  },

  // 刷新记录
  refreshRecords: function () {
    this.setData({
      page: 1,
      hasMore: true
    })
    this.loadPaymentRecords()
  },

  // 空状态操作
  handleEmptyAction: function () {
    if (this.data.timeFilter !== 'all' || this.data.typeFilter !== 'all' || this.data.statusFilter !== 'all') {
      this.resetFilters()
    } else {
      this.refreshRecords()
    }
  },

  // 查看缴费详情
  viewPaymentDetail: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/servicePackage/pages/payment/detail/detail?id=${id}`
    })
  },

  // 查看电子发票
  downloadInvoice: function (e) {
    const id = e.currentTarget.dataset.id

    // 跳转到发票预览页面
    wx.navigateTo({
      url: `/pages/payment/invoice/invoice?id=${id}`
    })
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack()
  },

  // 跳转到缴费设置页面
  navigateToSettings: function () {
    wx.navigateTo({
      url: '/pages/payment/settings/settings'
    })
  },

  // 跳转到缴费分析页面
  navigateToAnalysis: function () {
    wx.navigateTo({
      url: '/pages/payment/analysis/analysis'
    })
  }
})
