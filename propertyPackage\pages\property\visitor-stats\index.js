// 访客统计页面
const propertyApi = require('@/api/propertyApi.js')
Page({
  data: {
    activeTab: 'trend', // 当前激活的标签页：trend-趋势分析，duration-滞留时长，purpose-来访目的，parking-停车占用
    timeRange: 'month', // 时间范围：month-月度，week-周度
    trendData: {}, // 趋势数据
    durationData: {}, // 滞留时长数据
    purposeData: {}, // 来访目的数据
    parkingData: {}, // 停车占用数据
    isLoading: false, // 是否正在加载
    chartRendered: false, // 图表是否已渲染
    timeLevel: 0,
    averageStayDuration: 0,
    purposeDistribution: {},
    stayDurationDistribution: {},
    timeSlotDistribution: {},
    todayVisited: 0,
    totalVisitors: 0,
    trendData: [],
    vehicleParkingDuration: {}

  },

  onLoad: function () {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '访客统计'
    });

    // 加载统计数据
    this.loadStatisticsData();
  },

  onShow: function () {
    // 如果图表未渲染，则渲染图表
    if (!this.data.chartRendered) {
      this.renderCharts();
    }
  },

  // 切换标签页
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });

    // 切换标签页后重新渲染图表
    this.renderCharts();
  },

  // 切换时间范围
  switchTimeRange: function (e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      timeRange: range
    });

    // 切换时间范围后重新加载数据并渲染图表
    this.loadStatisticsData();
  },

  // 加载统计数据
  loadStatisticsData: function () {
    this.setData({ isLoading: true });


    const params = {
      timeLevel: this.data.timeLevel,
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    propertyApi.getVisitorStatistics(params).then(res => {

      console.log('res', res)
    })
  },

  // 生成趋势数据
  generateTrendData: function (visitors) {
    // 根据时间范围生成不同的数据
    const isMonth = this.data.timeRange === 'month';
    const days = isMonth ? 30 : 7;
    const labels = [];
    const data = [];

    // 生成日期标签
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      const label = `${date.getMonth() + 1}/${date.getDate()}`;
      labels.push(label);

      // 生成随机数据
      data.push(Math.floor(Math.random() * 50) + 10);
    }

    return {
      labels,
      data,
      total: data.reduce((sum, value) => sum + value, 0)
    };
  },

  // 生成滞留时长数据
  generateDurationData: function (visitors) {
    // 滞留时长分布
    const durations = [
      { label: '0-30分钟', value: Math.floor(Math.random() * 30) + 10 },
      { label: '30-60分钟', value: Math.floor(Math.random() * 40) + 20 },
      { label: '1-2小时', value: Math.floor(Math.random() * 50) + 30 },
      { label: '2-3小时', value: Math.floor(Math.random() * 40) + 20 },
      { label: '3-4小时', value: Math.floor(Math.random() * 30) + 10 },
      { label: '4小时以上', value: Math.floor(Math.random() * 20) + 5 }
    ];

    // 热力图数据
    const heatmap = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let day = 0; day < 7; day++) {
        heatmap.push({
          hour,
          day,
          value: Math.floor(Math.random() * 10)
        });
      }
    }

    return {
      durations,
      heatmap,
      avgDuration: Math.floor(Math.random() * 120) + 60 // 平均滞留时长（分钟）
    };
  },

  // 生成来访目的数据
  generatePurposeData: function (visitors) {
    const purposes = [
      { label: '探亲访友', value: Math.floor(Math.random() * 100) + 50 },
      { label: '快递配送', value: Math.floor(Math.random() * 80) + 40 },
      { label: '家政服务', value: Math.floor(Math.random() * 60) + 30 },
      { label: '商务拜访', value: Math.floor(Math.random() * 40) + 20 },
      { label: '维修服务', value: Math.floor(Math.random() * 30) + 15 },
      { label: '其他', value: Math.floor(Math.random() * 20) + 10 }
    ];

    // 计算总数
    const total = purposes.reduce((sum, item) => sum + item.value, 0);

    // 计算百分比
    const purposesWithPercentage = purposes.map(item => ({
      ...item,
      percentage: Math.round((item.value / total) * 100)
    }));

    return {
      purposes: purposesWithPercentage,
      total
    };
  },

  // 生成停车占用数据
  generateParkingData: function (visitors) {
    // 筛选有车的访客
    const visitorWithCar = visitors.filter(visitor => visitor.carNumber);

    // 停车时长分布
    const parkingDurations = [
      { label: '0-1小时', value: Math.floor(Math.random() * 30) + 10 },
      { label: '1-2小时', value: Math.floor(Math.random() * 40) + 20 },
      { label: '2-4小时', value: Math.floor(Math.random() * 50) + 30 },
      { label: '4-8小时', value: Math.floor(Math.random() * 40) + 20 },
      { label: '8小时以上', value: Math.floor(Math.random() * 30) + 10 }
    ];

    // 停车占用率
    const occupancyRate = Math.floor(Math.random() * 30) + 20; // 20%-50%

    // 每小时停车占用率
    const hourlyOccupancy = [];
    for (let hour = 0; hour < 24; hour++) {
      hourlyOccupancy.push({
        hour,
        rate: Math.floor(Math.random() * 50) + 10 // 10%-60%
      });
    }

    return {
      parkingDurations,
      occupancyRate,
      hourlyOccupancy,
      totalCars: visitorWithCar.length || Math.floor(Math.random() * 100) + 50
    };
  },

  // 渲染图表
  renderCharts: function () {
    // 根据当前标签页渲染不同的图表
    switch (this.data.activeTab) {
      case 'trend':
        this.renderTrendChart();
        break;
      case 'duration':
        this.renderDurationChart();
        break;
      case 'purpose':
        this.renderPurposeChart();
        break;
      case 'parking':
        this.renderParkingChart();
        break;
    }

    this.setData({ chartRendered: true });
  },

  // 渲染趋势图表
  renderTrendChart: function () {
    // 在实际应用中，这里应该使用图表库渲染图表
    console.log('渲染趋势图表', this.data.trendData);

    // 模拟渲染图表
    wx.showToast({
      title: '图表已渲染',
      icon: 'none',
      duration: 1000
    });
  },

  // 渲染滞留时长图表
  renderDurationChart: function () {
    // 在实际应用中，这里应该使用图表库渲染图表
    console.log('渲染滞留时长图表', this.data.durationData);

    // 模拟渲染图表
    wx.showToast({
      title: '图表已渲染',
      icon: 'none',
      duration: 1000
    });
  },

  // 渲染来访目的图表
  renderPurposeChart: function () {
    // 在实际应用中，这里应该使用图表库渲染图表
    console.log('渲染来访目的图表', this.data.purposeData);

    // 模拟渲染图表
    wx.showToast({
      title: '图表已渲染',
      icon: 'none',
      duration: 1000
    });
  },

  // 渲染停车占用图表
  renderParkingChart: function () {
    // 在实际应用中，这里应该使用图表库渲染图表
    console.log('渲染停车占用图表', this.data.parkingData);

    // 模拟渲染图表
    wx.showToast({
      title: '图表已渲染',
      icon: 'none',
      duration: 1000
    });
  },

  // 导出统计数据
  exportStatistics: function () {
    wx.showToast({
      title: '数据导出功能开发中',
      icon: 'none'
    });
  }
});
